using Microsoft.Extensions.Logging;
using Quartz;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Jobs
{
    /// <summary>
    /// Background job for automated daily email processing at midnight Greece time
    /// </summary>
    [DisallowConcurrentExecution]
    public class DailyEmailProcessingJob : IJob
    {
        private readonly IEmailProcessingService _emailProcessingService;
        private readonly ILogger<DailyEmailProcessingJob> _logger;

        public DailyEmailProcessingJob(
            IEmailProcessingService emailProcessingService,
            ILogger<DailyEmailProcessingJob> logger)
        {
            _emailProcessingService = emailProcessingService;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var jobId = Guid.NewGuid();
            var startTime = DateTime.UtcNow;
            
            _logger.LogInformation("🕛 [SCHEDULED JOB] Starting daily email processing job {JobId} at {StartTime} UTC", 
                jobId, startTime);
            
            try
            {
                // Use system user ID (could be configurable in the future)
                var systemUserId = Guid.Empty; // This represents the system/scheduled execution
                
                // Use default processing options for scheduled runs
                var processOptions = new ProcessEmailsDto
                {
                    DaysBack = 7,           // Look back 7 days for emails
                    ForceReprocess = false  // Don't reprocess already processed emails
                };

                _logger.LogInformation("📧 [SCHEDULED JOB] Processing emails from the last {DaysBack} days", 
                    processOptions.DaysBack);

                // Execute the same processing logic used for manual processing
                var result = await _emailProcessingService.ProcessEmailsAsync(processOptions, systemUserId);

                var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;

                if (result.Exception == null)
                {
                    _logger.LogInformation("✅ [SCHEDULED JOB] Daily email processing completed successfully in {Duration}ms. Job ID: {JobId}", 
                        duration, jobId);
                    
                    if (result.Payload != null)
                    {
                        _logger.LogInformation("📊 [SCHEDULED JOB] Processing results: {ProcessedEmails}/{TotalEmails} emails processed", 
                            result.Payload.ProcessedEmails, result.Payload.TotalEmails);
                    }
                }
                else
                {
                    _logger.LogError("❌ [SCHEDULED JOB] Daily email processing failed after {Duration}ms. Job ID: {JobId}. Error: {Error}", 
                        duration, jobId, result.Exception.Description);
                }
            }
            catch (Exception ex)
            {
                var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;
                _logger.LogError(ex, "💥 [SCHEDULED JOB] Fatal error during scheduled email processing after {Duration}ms. Job ID: {JobId}", 
                    duration, jobId);
                
                // Don't rethrow - we want the job to complete even if processing fails
                // Quartz will handle retries based on configuration
            }
            
            _logger.LogInformation("🏁 [SCHEDULED JOB] Daily email processing job {JobId} completed", jobId);
        }
    }
}