using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.ControllersExceptions;
using System.Text.Json;

namespace SmartBoat.API.Services
{
    public partial class SensorDataPointService
    {
        public async Task<string> Create(CreateSensorDataPointDto request, Guid userId)
        {
            if (request == null || request.SensorId == Guid.Empty || request.Measurements == null || !request.Measurements.Any())
            {
                _logger.LogInformation($"SB-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("SB-422", "Client Error");
            }

            SensorDataPoint sensorDataPoint = new SensorDataPoint();

            var isAuthorizedDto = new IsAuthorizedDto<SensorDataPoint>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = sensorDataPoint
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            // Verify sensor exists and user has access
            var sensor = await _databaseService.SelectByIdAsync<Sensor, Guid>(request.SensorId);
            if (sensor == null)
            {
                _logger.LogInformation($"SB-404: Technical Error. Sensor not found with Id: {request.SensorId}. UserId: {userId}");
                throw new TechnicalException("SB-404", "Technical Error");
            }

            sensorDataPoint = new SensorDataPoint
            {
                Id = Guid.NewGuid(),
                SensorId = request.SensorId,
                Timestamp = request.Timestamp,
                TimestampUnix = ((DateTimeOffset)request.Timestamp).ToUnixTimeSeconds(),
                Measurements = JsonSerializer.Serialize(request.Measurements),
                QualityScore = request.QualityScore ?? 1.0f,
                Source = request.Source ?? "API",
                Created = DateTime.Now
            };

            try
            {
                await _databaseService.InsertAsync<SensorDataPoint>(sensorDataPoint);
                var insertedDataPoint = await _databaseService.SelectByIdAsync<SensorDataPoint, Guid>(sensorDataPoint.Id.Value);
                if (insertedDataPoint == null)
                {
                    _logger.LogInformation($"SB-404: Technical Error. SensorDataPoint not found with Id: {sensorDataPoint.Id}. UserId: {userId}");
                    throw new TechnicalException("SB-404", "Technical Error");
                }
                sensorDataPoint = insertedDataPoint;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"SB-500: Technical Error. Error while inserting SensorDataPoint with Id: {sensorDataPoint.Id}. UserId: {userId}. Error: {ex.Message}");
                throw new TechnicalException("SB-500", "Technical Error");
            }

            return sensorDataPoint.Id.ToString();
        }
    }
}