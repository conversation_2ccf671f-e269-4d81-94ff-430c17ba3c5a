using System;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using Microsoft.Extensions.Logging;
using Quartz;

namespace SmartBoat.API.Services
{
    public partial class EmailProcessingService
    {
        /// <summary>
        /// Enable or disable scheduled email processing
        /// </summary>
        public async Task<Response<bool>> ScheduleProcessingAsync(bool enabled, Guid userId)
        {
            try
            {
                _logger.LogInformation("⚙️ [SCHEDULE] Setting schedule status to {Enabled} for user {UserId}",
                    enabled, userId);

                // Get or create the global schedule record
                var schedule = await GetOrCreateGlobalScheduleAsync();

                if (schedule == null)
                {
                    _logger.LogWarning("⚠️ [SCHEDULE] Cannot access schedule configuration - database table may not exist");
                    return new Response<bool>
                    {
                        Payload = false,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "SCHEDULE-503",
                            Description = "Schedule configuration is not available. Database setup may be required.",
                            Category = "Service Unavailable"
                        }
                    };
                }

                // Update the schedule status
                schedule.IsEnabled = enabled;
                schedule.UpdatedAt = DateTime.UtcNow;
                schedule.UpdatedBy = userId.ToString();

                await _databaseService.UpdateAsync<EmailProcessingSchedule>(schedule, new { Id = schedule.Id });

                _logger.LogInformation("✅ [SCHEDULE] Schedule {Status} successfully",
                    enabled ? "enabled" : "disabled");

                return new Response<bool>
                {
                    Payload = enabled,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 [SCHEDULE] Error setting schedule status to {Enabled} for user {UserId}",
                    enabled, userId);

                return new Response<bool>
                {
                    Payload = false,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "SCHEDULE-500",
                        Description = "Unable to update schedule configuration. Please try again later.",
                        Category = "Technical Error"
                    }
                };
            }
        }

        /// <summary>
        /// Get current schedule status and next run time
        /// </summary>
        public async Task<Response<ScheduleStatusDto>> GetScheduleStatusAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("📅 [SCHEDULE] Getting schedule status for user {UserId}", userId);

                // Get the global schedule configuration
                var schedule = await GetOrCreateGlobalScheduleAsync();

                if (schedule == null)
                {
                    _logger.LogWarning("⚠️ [SCHEDULE] No schedule configuration found, returning disabled status");
                    return new Response<ScheduleStatusDto>
                    {
                        Payload = new ScheduleStatusDto
                        {
                            IsEnabled = false,
                            ScheduleType = "Daily",
                            Description = "Schedule not available - database table may not exist yet",
                            NextRunTime = null,
                            TimeZone = "Europe/Athens",
                            DaysBack = 7
                        },
                        Exception = null
                    };
                }

                // Calculate next run time if enabled
                DateTime? nextRunTime = null;
                if (schedule.IsEnabled)
                {
                    nextRunTime = await CalculateNextRunTimeAsync(schedule);
                }

                var statusDto = new ScheduleStatusDto
                {
                    IsEnabled = schedule.IsEnabled,
                    ScheduleType = schedule.ScheduleType,
                    Description = schedule.Description ?? "Daily email processing at midnight Greece time",
                    NextRunTime = nextRunTime,
                    TimeZone = schedule.TimeZone,
                    DaysBack = schedule.DaysBack
                };

                _logger.LogInformation("✅ [SCHEDULE] Schedule status retrieved: Enabled={Enabled}, NextRun={NextRun}",
                    statusDto.IsEnabled, statusDto.NextRunTime);

                return new Response<ScheduleStatusDto>
                {
                    Payload = statusDto,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 [SCHEDULE] Error getting schedule status for user {UserId}", userId);

                // Return a default disabled status instead of failing completely
                return new Response<ScheduleStatusDto>
                {
                    Payload = new ScheduleStatusDto
                    {
                        IsEnabled = false,
                        ScheduleType = "Daily",
                        Description = "Schedule temporarily unavailable",
                        NextRunTime = null,
                        TimeZone = "Europe/Athens",
                        DaysBack = 7
                    },
                    Exception = null
                };
            }
        }

        /// <summary>
        /// Update schedule configuration with detailed options
        /// </summary>
        public async Task<Response<ScheduleStatusDto>> UpdateScheduleAsync(UpdateScheduleDto updateScheduleDto, Guid userId)
        {
            try
            {
                _logger.LogInformation("🔧 [SCHEDULE] Updating schedule configuration for user {UserId}", userId);

                var schedule = await GetOrCreateGlobalScheduleAsync();

                if (schedule == null)
                {
                    _logger.LogWarning("⚠️ [SCHEDULE] Cannot update schedule - configuration not available");
                    return new Response<ScheduleStatusDto>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "SCHEDULE-503",
                            Description = "Schedule configuration is not available. Database setup may be required.",
                            Category = "Service Unavailable"
                        }
                    };
                }

                // Update the schedule with provided values
                schedule.IsEnabled = updateScheduleDto.IsEnabled;
                if (updateScheduleDto.DaysBack.HasValue)
                    schedule.DaysBack = updateScheduleDto.DaysBack.Value;
                if (updateScheduleDto.ForceReprocess.HasValue)
                    schedule.ForceReprocess = updateScheduleDto.ForceReprocess.Value;
                if (!string.IsNullOrEmpty(updateScheduleDto.Description))
                    schedule.Description = updateScheduleDto.Description;

                schedule.UpdatedAt = DateTime.UtcNow;
                schedule.UpdatedBy = userId.ToString();

                await _databaseService.UpdateAsync<EmailProcessingSchedule>(schedule, new { Id = schedule.Id });

                // Return updated status
                return await GetScheduleStatusAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 [SCHEDULE] Error updating schedule for user {UserId}", userId);

                return new Response<ScheduleStatusDto>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "SCHEDULE-500",
                        Description = "Unable to update schedule configuration. Please try again later.",
                        Category = "Technical Error"
                    }
                };
            }
        }

        /// <summary>
        /// Get or create the global schedule configuration
        /// </summary>
        private async Task<EmailProcessingSchedule?> GetOrCreateGlobalScheduleAsync()
        {
            try
            {
                _logger.LogInformation("🔍 [SCHEDULE] Looking for existing global schedule configuration");

                // Get all schedules and filter for global schedule (where UserId IS NULL) in code
                // This avoids SQL syntax issues with NULL comparisons
                var allSchedules = await _databaseService.SelectAsync<EmailProcessingSchedule>(new { });
                var globalSchedule = allSchedules?.Where(s => s.UserId == null).FirstOrDefault();

                _logger.LogInformation("📊 [SCHEDULE] Found {Count} total schedules, {GlobalCount} global schedules",
                    allSchedules?.Count() ?? 0,
                    allSchedules?.Where(s => s.UserId == null).Count() ?? 0);

                if (globalSchedule != null)
                {
                    _logger.LogInformation("✅ [SCHEDULE] Using existing global schedule with ID {ScheduleId}", globalSchedule.Id);
                    return globalSchedule;
                }

                // Create default global schedule if none exists
                _logger.LogInformation("🆕 [SCHEDULE] No global schedule found, creating default configuration");

                var newSchedule = new EmailProcessingSchedule
                {
                    Id = Guid.NewGuid(),
                    UserId = null, // Global schedule
                    IsEnabled = false, // Disabled by default
                    ScheduleType = "Daily",
                    CronExpression = "0 0 0 * * ?", // Daily at midnight
                    TimeZone = "Europe/Athens",
                    DaysBack = 7,
                    ForceReprocess = false,
                    Description = "Default daily email processing schedule at midnight Greece time",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedBy = "SYSTEM"
                };

                _logger.LogInformation("💾 [SCHEDULE] Inserting new global schedule into database");
                await _databaseService.InsertAsync(newSchedule);

                _logger.LogInformation("✅ [SCHEDULE] Default global schedule created successfully with ID {ScheduleId}", newSchedule.Id);
                return newSchedule;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ [SCHEDULE] Could not access EmailProcessingSchedule table: {ErrorMessage}", ex.Message);

                // Check if it's a table not found error
                if (ex.Message.Contains("Invalid object name 'EmailProcessingSchedule'") ||
                    ex.Message.Contains("doesn't exist") ||
                    ex.Message.Contains("not found"))
                {
                    _logger.LogInformation("📋 [SCHEDULE] EmailProcessingSchedule table does not exist yet. " +
                        "Schedule functionality will be limited until database is properly set up. " +
                        "Run: ./Database/setup-database.sh to create required tables.");
                }
                else if (ex.Message.Contains("Incorrect syntax near"))
                {
                    _logger.LogWarning("🔧 [SCHEDULE] SQL syntax error - this should now be fixed with the updated query");
                }

                // Return null to indicate schedule is not available, but don't crash the application
                return null;
            }
        }

        /// <summary>
        /// Calculate the next run time for the schedule
        /// </summary>
        private async Task<DateTime?> CalculateNextRunTimeAsync(EmailProcessingSchedule schedule)
        {
            try
            {
                // For daily schedule at midnight Greece time, calculate next midnight
                var greeceTimeZone = TimeZoneInfo.FindSystemTimeZoneById(schedule.TimeZone);
                var nowInGreece = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, greeceTimeZone);

                // Get next midnight in Greece time
                var nextMidnightGreece = nowInGreece.Date.AddDays(1); // Next day at 00:00

                // Convert back to UTC for storage/display
                var nextMidnightUtc = TimeZoneInfo.ConvertTimeToUtc(nextMidnightGreece, greeceTimeZone);

                return nextMidnightUtc;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ [SCHEDULE] Error calculating next run time, falling back to UTC calculation");

                // Fallback: next midnight UTC
                var nowUtc = DateTime.UtcNow;
                return nowUtc.Date.AddDays(1);
            }
        }
    }
}