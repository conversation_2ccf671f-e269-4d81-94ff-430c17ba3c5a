using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface ICsvProcessingService
    {
        Task<Response<List<CsvSensorData>>> ProcessCsvFileAsync(byte[] csvContent, string fileName, string sourceEmail);
        Task<Response<List<string>>> ExtractVesselNamesAsync(byte[] csvContent);
        Task<Response<bool>> ValidateCsvFormatAsync(byte[] csvContent);
    }
}