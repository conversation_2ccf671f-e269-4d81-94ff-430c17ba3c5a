using System;
using System.Collections.Generic;

namespace SmartBoat.API.Types
{
    public class SensorDataPointDto
    {
        public Guid Id { get; set; }
        public Guid SensorId { get; set; }
        public DateTime Timestamp { get; set; }
        public long TimestampUnix { get; set; }
        
        // All measurements as flexible dictionary
        public Dictionary<string, object> Measurements { get; set; } = new();
        
        // Common computed values for easy access
        public float? Temperature { get; set; }
        public float? Humidity { get; set; }
        public float? Speed { get; set; }
        public float? RPM { get; set; }
        
        // Metadata
        public float? QualityScore { get; set; }
        public string Source { get; set; }
        
        public DateTime Created { get; set; }
        public DateTime? Changed { get; set; }
        
        // Helper getters for common measurements
        public float? GetTemperature() => GetMeasurement<float>("temperature");
        public float? GetHumidity() => GetMeasurement<float>("humidity");
        public float? GetSpeed() => GetMeasurement<float>("speed");
        public float? GetRPM() => GetMeasurement<float>("rpm");
        public float? GetFuelLevel() => GetMeasurement<float>("fuel_level");
        public float? GetBatteryVoltage() => GetMeasurement<float>("battery_voltage");
        
        // GPS coordinates
        public (float? latitude, float? longitude) GetPosition()
        {
            if (Measurements.TryGetValue("gps", out var gps) && gps is Dictionary<string, object> coords)
            {
                var lat = coords.TryGetValue("latitude", out var latVal) ? Convert.ToSingle(latVal) : (float?)null;
                var lng = coords.TryGetValue("longitude", out var lngVal) ? Convert.ToSingle(lngVal) : (float?)null;
                return (lat, lng);
            }
            return (null, null);
        }
        
        // Wind data
        public (float? speed, float? direction) GetWind()
        {
            if (Measurements.TryGetValue("wind", out var wind) && wind is Dictionary<string, object> windData)
            {
                var speed = windData.TryGetValue("speed", out var speedVal) ? Convert.ToSingle(speedVal) : (float?)null;
                var direction = windData.TryGetValue("direction", out var dirVal) ? Convert.ToSingle(dirVal) : (float?)null;
                return (speed, direction);
            }
            return (null, null);
        }
        
        private T? GetMeasurement<T>(string key) where T : struct
        {
            if (Measurements.TryGetValue(key, out var value) && value != null)
            {
                try
                {
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    return null;
                }
            }
            return null;
        }
    }


    public class SensorDataPointRequestDto
    {
        public Guid Id { get; set; }
    }

    public class ListSensorDataPointRequestDto
    {
        public Guid? SensorId { get; set; }
        public List<Guid>? SensorIds { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int? Limit { get; set; }
        public string? OrderBy { get; set; } = "Timestamp";
        public bool? Descending { get; set; } = true;
    }

    public class ReturnListSensorDataPointDto
    {
        public List<SensorDataPointDto> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int PageNumber { get; set; }
    }

    public class LatestSensorDataPointRequestDto
    {
        public Guid? SensorId { get; set; }
        public List<Guid>? SensorIds { get; set; }
    }

    public class DeleteSensorDataPointDto
    {
        public Guid Id { get; set; }
    }

    public class UpdateSensorDataPointDto
    {
        public Guid Id { get; set; }
        public Dictionary<string, object>? Measurements { get; set; }
        public float? QualityScore { get; set; }
        public string? Source { get; set; }
    }
}