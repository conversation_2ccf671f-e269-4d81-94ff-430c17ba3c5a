using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Collections.Generic;

namespace SmartBoat.API.Types
{
    [Table("SensorDataPoints")]
    public class SensorDataPoint
    {
        public Guid? Id { get; set; }
        public Guid? SensorId { get; set; }
        public DateTime? Timestamp { get; set; }
        public long? TimestampUnix { get; set; }
        
        // JSON field for flexible measurements
        public string Measurements { get; set; }
        
        // Computed columns (read-only) - excluded from database operations
        [NotMapped]
        public float? Temperature { get; set; }
        
        [NotMapped]
        public float? Humidity { get; set; }
        
        [NotMapped]
        public float? Speed { get; set; }
        
        [NotMapped]
        public float? RPM { get; set; }
        
        // Metadata
        public float? QualityScore { get; set; }
        public string Source { get; set; }
        
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
        
        // Helper to get specific measurement value from JSON
        public T? GetMeasurement<T>(string key) where T : struct
        {
            if (string.IsNullOrEmpty(Measurements))
                return null;
                
            try
            {
                var data = JsonSerializer.Deserialize<Dictionary<string, object>>(Measurements);
                if (data?.TryGetValue(key, out var value) == true && value != null)
                {
                    if (value is JsonElement element)
                    {
                        return typeof(T) switch
                        {
                            Type t when t == typeof(float) => (T)(object)element.GetSingle(),
                            Type t when t == typeof(double) => (T)(object)element.GetDouble(),
                            Type t when t == typeof(int) => (T)(object)element.GetInt32(),
                            Type t when t == typeof(bool) => (T)(object)element.GetBoolean(),
                            _ => null
                        };
                    }
                    if (value is T directValue)
                        return directValue;
                }
            }
            catch
            {
                // Invalid JSON or conversion error
            }
            return null;
        }
    }
}