import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  useTokenStatus,
  useExchangeToken,
  useScheduleStatus,
  useProcessingSummary,
  useProcessEmails,
  useUpdateSchedule
} from '../../../hooks/queries/useEmailProcessingQueries';
import { startMicrosoftAuth, handleAuthCallback, isAuthCallback, isPrivateMode } from '../../../utils/microsoftAuth';

interface EmailProcessingSettingsProps {
  userData: any;
  isEditing: boolean;
}

const EmailProcessingSettings: React.FC<EmailProcessingSettingsProps> = ({
  userData,
  isEditing
}) => {
  const { t } = useTranslation();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const [isPrivateModeDetected, setIsPrivateModeDetected] = useState(false);
  const [syncMessage, setSyncMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const authProcessingRef = useRef(false);

  // React Query hooks
  const { data: tokenStatus, isLoading: tokenLoading, error: tokenError } = useTokenStatus();
  const { data: scheduleStatus, isLoading: scheduleLoading } = useScheduleStatus();
  const { data: processingSummary, isLoading: summaryLoading } = useProcessingSummary();
  const exchangeTokenMutation = useExchangeToken();
  const processEmailsMutation = useProcessEmails();
  const updateScheduleMutation = useUpdateSchedule();

  // Detect private/incognito mode on component mount
  useEffect(() => {
    setIsPrivateModeDetected(isPrivateMode());
  }, []);

  // Handle authentication callback on component mount
  useEffect(() => {
    const handleCallback = async () => {
      if (isAuthCallback() && !authProcessingRef.current) {
        // Prevent duplicate processing
        authProcessingRef.current = true;

        // Immediately clean up URL to prevent duplicate processing
        const urlParams = new URLSearchParams(window.location.search);
        window.history.replaceState({}, document.title, window.location.pathname + window.location.hash);

        setIsAuthenticating(true);
        setAuthError(null);
        try {
          const exchangeData = await handleAuthCallback(urlParams);

          // Exchange authorization code for tokens via server-side
          await exchangeTokenMutation.mutateAsync(exchangeData);

          // Success - clear any previous errors
          setAuthError(null);

        } catch (error) {
          console.error('Authentication callback failed:', error);

          // Set user-friendly error message
          let errorMessage = error.message;
          if (error.message.includes('browser storage unavailable')) {
            errorMessage = 'Authentication failed due to browser storage restrictions. Please try using a regular browser window instead of incognito mode.';
          } else if (error.message.includes('session expired')) {
            errorMessage = 'Authentication session expired. Please try again.';
          } else if (error.message.includes('CSRF attack')) {
            errorMessage = 'Security validation failed. Please try again.';
          } else if (error.message.includes('already redeemed')) {
            errorMessage = 'Authentication code has already been used. Please try authenticating again.';
          }

          setAuthError(errorMessage);
        } finally {
          setIsAuthenticating(false);
          // Reset the ref so user can try again if needed
          authProcessingRef.current = false;
        }
      }
    };

    handleCallback();
  }, [exchangeTokenMutation]);

  // Microsoft authentication handler
  const handleMicrosoftLogin = async (forceLogin = false) => {
    setIsAuthenticating(true);
    setAuthError(null);
    try {
      await startMicrosoftAuth({
        forceLogin,
        selectAccount: true
      });
    } catch (error) {
      console.error('Microsoft authentication failed:', error);

      // Set user-friendly error message
      let errorMessage = error.message;
      if (error.message.includes('Browser storage not available')) {
        errorMessage = 'Browser storage is not available. Please try using a regular browser window instead of incognito mode, or enable cookies and local storage.';
      }

      setAuthError(errorMessage);
      setIsAuthenticating(false);
    }
  };

  // Manual sync handler
  const handleManualSync = async (forceReprocess = false) => {
    setSyncMessage(null);
    try {
      const result = await processEmailsMutation.mutateAsync({
        daysBack: 7,
        forceReprocess
      });

      if (result?.payload) {
        const { processedEmails = 0, totalEmails = 0 } = result.payload;
        setSyncMessage({
          type: 'success',
          message: `Sync completed successfully! Processed ${processedEmails} of ${totalEmails} emails.`
        });
      } else {
        setSyncMessage({
          type: 'success',
          message: 'Sync completed successfully!'
        });
      }

      // Clear success message after 10 seconds
      setTimeout(() => setSyncMessage(null), 10000);
    } catch (error) {
      console.error('Manual sync failed:', error);
      setSyncMessage({
        type: 'error',
        message: error?.message || 'Sync failed. Please try again.'
      });

      // Clear error message after 15 seconds
      setTimeout(() => setSyncMessage(null), 15000);
    }
  };

  // Schedule toggle handler
  const handleScheduleToggle = async () => {
    try {
      const currentStatus = scheduleStatus?.payload?.isEnabled || false;
      const newStatus = !currentStatus;

      await updateScheduleMutation.mutateAsync({
        isEnabled: newStatus,
        daysBack: 7,
        description: `Email processing schedule ${newStatus ? 'enabled' : 'disabled'} by user`
      });

      setSyncMessage({
        type: 'success',
        message: `Scheduled processing ${newStatus ? 'enabled' : 'disabled'} successfully!`
      });

      // Clear success message after 5 seconds
      setTimeout(() => setSyncMessage(null), 5000);
    } catch (error) {
      console.error('Schedule toggle failed:', error);
      setSyncMessage({
        type: 'error',
        message: error?.message || 'Failed to update schedule. Please try again.'
      });

      // Clear error message after 10 seconds
      setTimeout(() => setSyncMessage(null), 10000);
    }
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return t('emailProcessing.never');
    return new Date(dateString).toLocaleString();
  };

  // Format next run time in Greece time
  const formatNextRunTime = (dateString: string | null) => {
    if (!dateString) return 'Not scheduled';

    try {
      const date = new Date(dateString);
      // Format in Greece time zone
      return date.toLocaleString('en-GB', {
        timeZone: 'Europe/Athens',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }) + ' (Greece time)';
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  // Calculate days until expiration
  const getDaysUntilExpiration = (expirationDate: string | null) => {
    if (!expirationDate) return null;
    const now = new Date();
    const expiry = new Date(expirationDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get status badge color
  const getStatusBadgeColor = (isConnected: boolean, daysUntilExpiry: number | null) => {
    if (!isConnected) return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    if (daysUntilExpiry !== null && daysUntilExpiry <= 5) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
  };

  const isConnected = tokenStatus?.hasToken && !tokenStatus?.isExpired;
  const daysUntilExpiry = tokenStatus?.expiresAt ? getDaysUntilExpiration(tokenStatus.expiresAt) : null;

  if (tokenLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Microsoft Login Section */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          {t('emailProcessing.microsoftLogin')}
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(isConnected, daysUntilExpiry)}`}
              >
                {isConnected ? t('emailProcessing.connected') : t('emailProcessing.notConnected')}
              </span>

              {isConnected && daysUntilExpiry !== null && daysUntilExpiry <= 5 && (
                <span className="text-sm text-yellow-600 dark:text-yellow-400">
                  {t('emailProcessing.reAuthRequired')}
                </span>
              )}

              {isPrivateModeDetected && (
                <span className="text-sm text-orange-600 dark:text-orange-400">
                  Private mode detected
                </span>
              )}
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => handleMicrosoftLogin(false)}
                disabled={isAuthenticating || exchangeTokenMutation.isPending}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                {isAuthenticating || exchangeTokenMutation.isPending ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    <span>Connecting...</span>
                  </div>
                ) : (
                  isConnected ? 'Re-authenticate' : t('emailProcessing.connectToMicrosoft')
                )}
              </button>

              {/* Use Different Account button */}
              <button
                onClick={() => handleMicrosoftLogin(true)}
                disabled={isAuthenticating || exchangeTokenMutation.isPending}
                className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                Use Different Account
              </button>
            </div>
          </div>

          {/* Private mode warning */}
          {isPrivateModeDetected && (
            <div className="p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-orange-800 dark:text-orange-200">
                    <strong>Private/Incognito Mode Detected:</strong> Authentication may be limited due to browser storage restrictions.
                    If you experience issues, please try using a regular browser window.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Authentication error display */}
          {authError && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    <strong>Authentication Error:</strong> {authError}
                  </p>
                  <button
                    onClick={() => setAuthError(null)}
                    className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {tokenError && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Service Configuration Issue
                </h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>
                    The email processing service is not fully configured. This may be due to missing database tables.
                  </p>
                  <p className="mt-2">
                    <strong>For administrators:</strong> Please run the database setup script to create the required tables:
                  </p>
                  <div className="mt-2 p-2 bg-red-100 dark:bg-red-800/30 rounded font-mono text-xs">
                    ./Database/setup-database.sh
                  </div>
                  <p className="mt-2">
                    Or run the specific email processing setup:
                  </p>
                  <div className="mt-1 p-2 bg-red-100 dark:bg-red-800/30 rounded font-mono text-xs">
                    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U SA -P YourStrong@Passw0rd -C -d Smartboat &lt; Database/setup-email-processing-tables.sql
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Token Status Section */}
      {isConnected && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            {t('emailProcessing.tokenStatus')}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('emailProcessing.tokenExpires')}
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {formatDate(tokenStatus?.expiresAt)}
              </dd>
            </div>

            {daysUntilExpiry !== null && (
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Days Remaining
                </dt>
                <dd className={`mt-1 text-sm font-medium ${
                  daysUntilExpiry <= 5
                    ? 'text-red-600 dark:text-red-400'
                    : 'text-green-600 dark:text-green-400'
                }`}>
                  {daysUntilExpiry} days
                </dd>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Sync Information Section */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Sync Information
          </h3>

          {/* Manual Sync Buttons */}
          {isConnected && (
            <div className="flex space-x-2">
              <button
                onClick={() => handleManualSync(false)}
                disabled={processEmailsMutation.isPending}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                title="Sync emails from the last 7 days"
              >
                {processEmailsMutation.isPending ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    <span>Syncing...</span>
                  </div>
                ) : (
                  'Sync Now'
                )}
              </button>

              <button
                onClick={() => handleManualSync(true)}
                disabled={processEmailsMutation.isPending}
                className="bg-orange-600 hover:bg-orange-700 disabled:bg-orange-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                title="Force reprocess all emails from the last 7 days"
              >
                Force Sync
              </button>
            </div>
          )}
        </div>

        {/* Scheduled Processing Toggle */}
        {isConnected && (
          <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200">
                  Scheduled Processing
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  Automatically process emails daily at midnight (Greece time)
                </p>
              </div>
              <button
                onClick={handleScheduleToggle}
                disabled={updateScheduleMutation.isPending || scheduleLoading}
                className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  scheduleStatus?.payload?.isEnabled
                    ? 'bg-blue-600'
                    : 'bg-gray-200 dark:bg-gray-700'
                } ${updateScheduleMutation.isPending ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <span className="sr-only">Toggle scheduled processing</span>
                <span
                  className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition duration-200 ease-in-out ${
                    scheduleStatus?.payload?.isEnabled ? 'translate-x-5' : 'translate-x-0'
                  }`}
                />
              </button>
            </div>
          </div>
        )}

        {/* Sync Success/Error Messages */}
        {syncMessage && (
          <div className={`mb-4 p-3 rounded-md ${
            syncMessage.type === 'success'
              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
              : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {syncMessage.type === 'success' ? (
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm ${
                  syncMessage.type === 'success'
                    ? 'text-green-800 dark:text-green-200'
                    : 'text-red-800 dark:text-red-200'
                }`}>
                  {syncMessage.message}
                </p>
                <button
                  onClick={() => setSyncMessage(null)}
                  className={`mt-1 text-sm underline ${
                    syncMessage.type === 'success'
                      ? 'text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200'
                      : 'text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200'
                  }`}
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        {summaryLoading || scheduleLoading ? (
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('emailProcessing.lastSync')}
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {formatDate(processingSummary?.recentProcessing?.[0]?.processedAt)}
              </dd>
              {processingSummary?.recentProcessing?.[0] && (
                <dd className="mt-1">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                    processingSummary.recentProcessing[0].processingStatus === 'Success'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {processingSummary.recentProcessing[0].processingStatus === 'Success'
                      ? t('emailProcessing.syncSuccessful')
                      : t('emailProcessing.syncFailed')
                    }
                  </span>
                </dd>
              )}
            </div>

            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t('emailProcessing.nextSync')}
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {scheduleStatus?.payload?.isEnabled
                  ? (scheduleStatus?.payload?.nextRunTime
                      ? formatNextRunTime(scheduleStatus.payload.nextRunTime)
                      : 'Daily at 12:00 AM (Greece time)')
                  : 'Not scheduled'}
              </dd>
              <dd className="mt-1">
                <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                  scheduleStatus?.payload?.isEnabled
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                  {scheduleStatus?.payload?.isEnabled ? 'Enabled' : 'Disabled'}
                </span>
              </dd>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailProcessingSettings;