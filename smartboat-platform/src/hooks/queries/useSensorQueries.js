/**
 * Sensor Query Hooks
 * Custom React Query hooks for sensor data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { sensorService } from '../../services';
import { queryKeys } from '../../config/queryClient';

/**
 * Hook to fetch all sensors
 */
export const useSensors = (options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback = false, ...queryOptions } = options;
  
  return useQuery({
    queryKey: queryKeys.sensors.lists(),
    queryFn: () => sensorService.getAllSensors({ useMockFallback }),
    ...queryOptions,
  });
};

/**
 * Hook to fetch a sensor by ID
 */
export const useSensor = (id, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback = false, ...queryOptions } = options;
  
  // Debug logging
  console.log('useSensor hook called with:', { id, useMockFallback, enabled: !!id });
  
  return useQuery({
    queryKey: queryKeys.sensors.detail(id),
    queryFn: () => {
      console.log('useSensor queryFn executing for id:', id);
      return sensorService.getSensorById(id, { useMockFallback });
    },
    enabled: !!id,
    ...queryOptions,
  });
};

/**
 * Hook to fetch sensors by vessel ID
 */
export const useSensorsByVessel = (vesselId, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback = false, ...queryOptions } = options;
  
  return useQuery({
    queryKey: queryKeys.sensors.byVessel(vesselId),
    queryFn: () => sensorService.getSensorsByVessel(vesselId, { useMockFallback }),
    enabled: !!vesselId,
    ...queryOptions,
  });
};

/**
 * Hook to fetch sensor data readings
 */
export const useSensorData = (id, params = {}, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback = false, ...queryOptions } = options;
  
  return useQuery({
    queryKey: queryKeys.sensors.data(id, params),
    queryFn: () => sensorService.getSensorData(id, params, { useMockFallback }),
    enabled: !!id,
    // Shorter stale time for sensor data that changes frequently
    staleTime: 60 * 1000, // 1 minute
    ...queryOptions,
  });
};

/**
 * Hook to fetch sensor alerts
 */
export const useSensorAlerts = (id, params = {}, options = {}) => {
  return useQuery({
    queryKey: queryKeys.sensors.alerts(id, params),
    queryFn: () => sensorService.getSensorAlerts(id, params),
    enabled: !!id,
    // Shorter stale time for alerts that change frequently
    staleTime: 60 * 1000, // 1 minute
    ...options,
  });
};

/**
 * Hook to create a new sensor
 */
export const useCreateSensor = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (sensorData) => sensorService.createSensor(sensorData, { useMockFallback: false }),
    onSuccess: (data) => {
      // Invalidate sensors list and vessel sensors
      queryClient.invalidateQueries(queryKeys.sensors.lists());
      if (data.vesselId) {
        queryClient.invalidateQueries(queryKeys.sensors.byVessel(data.vesselId));
      }
    },
    ...options,
  });
};

/**
 * Hook to update a sensor
 */
export const useUpdateSensor = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, sensorData }) => sensorService.updateSensor(id, sensorData, { useMockFallback: false }),
    onSuccess: (data, variables) => {
      // Invalidate specific sensor and lists
      queryClient.invalidateQueries(queryKeys.sensors.detail(variables.id));
      queryClient.invalidateQueries(queryKeys.sensors.lists());
      if (data.vesselId) {
        queryClient.invalidateQueries(queryKeys.sensors.byVessel(data.vesselId));
      }
    },
    ...options,
  });
};

/**
 * Hook to delete a sensor
 */
export const useDeleteSensor = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => sensorService.deleteSensor(id, { useMockFallback: false }),
    onSuccess: async (_, id) => {
      // Get vessel ID from cache if possible
      const cachedSensor = queryClient.getQueryData(queryKeys.sensors.detail(id));
      const vesselId = cachedSensor?.vesselId;
      
      // Invalidate sensor lists with immediate refetch
      await queryClient.invalidateQueries({
        queryKey: queryKeys.sensors.lists(),
        refetchType: 'all'
      });
      
      if (vesselId) {
        await queryClient.invalidateQueries({
          queryKey: queryKeys.sensors.byVessel(vesselId),
          refetchType: 'all'
        });
      }
      
      // Remove sensor from cache
      queryClient.removeQueries(queryKeys.sensors.detail(id));
    },
    ...options,
  });
};

/**
 * Hook to update sensor thresholds
 */
export const useUpdateSensorThresholds = (options = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, thresholdData }) => sensorService.updateSensorThresholds(id, thresholdData),
    onSuccess: (data, variables) => {
      // Invalidate specific sensor
      queryClient.invalidateQueries(queryKeys.sensors.detail(variables.id));
    },
    ...options,
  });
};